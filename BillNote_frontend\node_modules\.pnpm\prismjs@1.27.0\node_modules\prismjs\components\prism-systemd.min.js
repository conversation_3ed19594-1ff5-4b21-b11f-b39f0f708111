!function(e){var t={pattern:/^[;#].*/m,greedy:!0},n='"(?:[^\r\n"\\\\]|\\\\(?:[^\r]|\r\n?))*"(?!\\S)';Prism.languages.systemd={comment:t,section:{pattern:/^\[[^\n\r\[\]]*\](?=[ \t]*$)/m,greedy:!0,inside:{punctuation:/^\[|\]$/,"section-name":{pattern:/[\s\S]+/,alias:"selector"}}},key:{pattern:/^[^\s=]+(?=[ \t]*=)/m,greedy:!0,alias:"attr-name"},value:{pattern:RegExp("(=[ \t]*(?!\\s))(?:"+n+'|(?=[^"\r\n]))(?:[^\\s\\\\]|[ \t]+(?:(?![ \t"])|'+n+")|\\\\[\r\n]+(?:[#;].*[\r\n]+)*(?![#;]))*"),lookbehind:!0,greedy:!0,alias:"attr-value",inside:{comment:t,quoted:{pattern:RegExp("(^|\\s)"+n),lookbehind:!0,greedy:!0},punctuation:/\\$/m,boolean:{pattern:/^(?:false|no|off|on|true|yes)$/,greedy:!0}}},punctuation:/=/}}();