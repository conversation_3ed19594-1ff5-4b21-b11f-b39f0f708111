!function(e){function a(e,r){return e.replace(/<<(\d+)>>/g,function(e,n){return"(?:"+r[+n]+")"})}function n(e,n,r){return RegExp(a(e,n),r||"")}var r=RegExp("\\b(?:"+("Adj BigInt Bool Ctl Double false Int One Pauli PauliI PauliX PauliY PauliZ Qubit Range Result String true Unit Zero"+" "+"Adjoint adjoint apply as auto body borrow borrowing Controlled controlled distribute elif else fail fixup for function if in internal intrinsic invert is let mutable namespace new newtype open operation repeat return self set until use using while within").trim().replace(/ /g,"|")+")\\b"),t=a("<<0>>(?:\\s*\\.\\s*<<0>>)*",["\\b[A-Za-z_]\\w*\\b"]),i={keyword:r,punctuation:/[<>()?,.:[\]]/},s='"(?:\\\\.|[^\\\\"])*"';e.languages.qsharp=e.languages.extend("clike",{comment:/\/\/.*/,string:[{pattern:n("(^|[^$\\\\])<<0>>",[s]),lookbehind:!0,greedy:!0}],"class-name":[{pattern:n("(\\b(?:as|open)\\s+)<<0>>(?=\\s*(?:;|as\\b))",[t]),lookbehind:!0,inside:i},{pattern:n("(\\bnamespace\\s+)<<0>>(?=\\s*\\{)",[t]),lookbehind:!0,inside:i}],keyword:r,number:/(?:\b0(?:x[\da-f]+|b[01]+|o[0-7]+)|(?:\B\.\d+|\b\d+(?:\.\d*)?)(?:e[-+]?\d+)?)l?\b/i,operator:/\band=|\bor=|\band\b|\bnot\b|\bor\b|<[-=]|[-=]>|>>>=?|<<<=?|\^\^\^=?|\|\|\|=?|&&&=?|w\/=?|~~~|[*\/+\-^=!%]=?/,punctuation:/::|[{}[\];(),.:]/}),e.languages.insertBefore("qsharp","number",{range:{pattern:/\.\./,alias:"operator"}});var o=function(e,n){for(var r=0;r<n;r++)e=e.replace(/<<self>>/g,function(){return"(?:"+e+")"});return e.replace(/<<self>>/g,"[^\\s\\S]")}(a('\\{(?:[^"{}]|<<0>>|<<self>>)*\\}',[s]),2);e.languages.insertBefore("qsharp","string",{"interpolation-string":{pattern:n('\\$"(?:\\\\.|<<0>>|[^\\\\"{])*"',[o]),greedy:!0,inside:{interpolation:{pattern:n("((?:^|[^\\\\])(?:\\\\\\\\)*)<<0>>",[o]),lookbehind:!0,inside:{punctuation:/^\{|\}$/,expression:{pattern:/[\s\S]+/,alias:"language-qsharp",inside:e.languages.qsharp}}},string:/[\s\S]+/}}})}(Prism),Prism.languages.qs=Prism.languages.qsharp;