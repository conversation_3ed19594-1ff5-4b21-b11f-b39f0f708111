pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Cave
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-cave
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #19171c  Default Background
base01  #26232a  Lighter Background (Used for status bars, line number and folding marks)
base02  #585260  Selection Background
base03  #655f6d  Comments, Invisibles, Line Highlighting
base04  #7e7887  Dark Foreground (Used for status bars)
base05  #8b8792  Default Foreground, Caret, Delimiters, Operators
base06  #e2dfe7  Light Foreground (Not often used)
base07  #efecf4  Light Background (Not often used)
base08  #be4678  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #aa573c  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #a06e3b  Classes, Markup Bold, Search Text Background
base0B  #2a9292  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #398bc6  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #576ddb  Functions, Methods, Attribute IDs, Headings
base0E  #955ae7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #bf40bf  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #8b8792;
  background: #19171c
}
.hljs::selection,
.hljs ::selection {
  background-color: #585260;
  color: #8b8792
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #655f6d -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #655f6d
}
/* base04 - #7e7887 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #7e7887
}
/* base05 - #8b8792 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #8b8792
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #be4678
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #aa573c
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #a06e3b
}
.hljs-strong {
  font-weight: bold;
  color: #a06e3b
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #2a9292
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #398bc6
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #576ddb
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #955ae7
}
.hljs-emphasis {
  color: #955ae7;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #bf40bf
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}