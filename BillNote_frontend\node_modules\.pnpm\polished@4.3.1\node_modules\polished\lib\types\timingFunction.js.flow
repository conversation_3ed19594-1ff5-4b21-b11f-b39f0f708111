// @flow

// Note: we define properties with JSdoc since documentation.js doesn't recognize
// exported types yet. See https://github.com/documentationjs/documentation/issues/680

/**
 * @property { 'easeInBack', 'easeInCirc', 'easeInCubic', 'easeInExpo', 'easeInQuad', 'easeInQuart', 'easeInQuint', 'easeInSine', 'easeOutBack', 'easeOutCubic', 'easeOutCirc', 'easeOutExpo', 'easeOutQuad', 'easeOutQuart', 'easeOutQuint', 'easeOutSine', 'easeInOutBack', 'easeInOutCirc', 'easeInOutCubic', 'easeInOutExpo', 'easeInOutQuad', 'easeInOutQuart', 'easeInOutQuint', 'easeInOutSine' } TimingFunction
 */

export type TimingFunction =
  | 'easeInBack'
  | 'easeInCirc'
  | 'easeInCubic'
  | 'easeInExpo'
  | 'easeInQuad'
  | 'easeInQuart'
  | 'easeInQuint'
  | 'easeInSine'
  | 'easeOutBack'
  | 'easeOutCubic'
  | 'easeOutCirc'
  | 'easeOutExpo'
  | 'easeOutQuad'
  | 'easeOutQuart'
  | 'easeOutQuint'
  | 'easeOutSine'
  | 'easeInOutBack'
  | 'easeInOutCirc'
  | 'easeInOutCubic'
  | 'easeInOutExpo'
  | 'easeInOutQuad'
  | 'easeInOutQuart'
  | 'easeInOutQuint'
  | 'easeInOutSine'
