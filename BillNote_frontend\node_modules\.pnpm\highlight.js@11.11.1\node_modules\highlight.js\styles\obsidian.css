pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/**
 * Obsidian style
 * ported by <PERSON> (http://github.com/ioncreature)
 */
.hljs {
  color: #e0e2e4;
  background: #282b2e
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-selector-id {
  color: #93c763
}
.hljs-number {
  color: #ffcd22
}
.hljs-attribute {
  color: #668bb0
}
.hljs-regexp,
.hljs-link {
  color: #d39745
}
.hljs-meta {
  color: #557182
}
.hljs-tag,
.hljs-name,
.hljs-bullet,
.hljs-subst,
.hljs-emphasis,
.hljs-type,
.hljs-built_in,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: #8cbbad
}
.hljs-string,
.hljs-symbol {
  color: #ec7600
}
.hljs-comment,
.hljs-quote,
.hljs-deletion {
  color: #818e96
}
.hljs-selector-class {
  color: #A082BD
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-doctag,
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-name,
.hljs-strong {
  font-weight: bold
}
.hljs-code,
.hljs-title.class_,
.hljs-class .hljs-title,
.hljs-section {
  color: white
}