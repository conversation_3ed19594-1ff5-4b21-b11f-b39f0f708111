// @flow

// Note: we define properties with JSdoc since documentation.js doesn't recognize
// exported types yet. See https://github.com/documentationjs/documentation/issues/680

/**
 * @property {Array<string>}  colorStops
 * @property {?string}  extent
 * @property {?string}  fallback
 * @property {?string}  position
 * @property {?string}  shape
 */
export type RadialGradientConfiguration = {
  colorStops: Array<string>,
  extent?: string,
  fallback?: string,
  position?: string,
  shape?: string,
}
