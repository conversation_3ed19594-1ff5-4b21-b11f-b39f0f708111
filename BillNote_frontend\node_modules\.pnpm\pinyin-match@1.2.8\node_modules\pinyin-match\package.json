{"name": "pinyin-match", "version": "1.2.8", "description": "支持多音字, 繁体字，首字母缩写的拼音搜索引擎", "typings": "index.d.ts", "main": "lib/main.js", "unpkg": "dist/main.js", "module": "es/main.js", "scripts": {"test": "mocha", "build": "cross-env NODE_ENV=production rollup -c", "dev": "rollup -c -w"}, "keywords": ["拼音", "chinese", "pinyin", "match"], "repository": {"type": "git", "url": "https://github.com/xmflswood/pinyin-match.git"}, "author": "<EMAIL>", "license": "SATA", "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.7", "babel-plugin-add-module-exports": "^1.0.4", "cross-env": "^7.0.3", "mocha": "^5.2.0", "rollup": "^2.50.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-serve": "^1.1.0", "rollup-plugin-terser": "^7.0.2"}}