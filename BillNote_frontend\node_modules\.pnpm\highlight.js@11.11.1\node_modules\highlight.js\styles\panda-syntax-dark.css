pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/**
 * Panda Syntax Theme for Highlight.js
 * Based on: https://github.com/tinkertrain/panda-syntax-vscode
 * Author: <PERSON><PERSON><PERSON> <https://github.com/ann<PERSON>ie-switzer>
 */
.hljs {
  color: #e6e6e6;
  background: #2a2c2d
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
.hljs-link {
  text-decoration: underline
}
.hljs-comment,
.hljs-quote {
  color: #bbbbbb;
  font-style: italic
}
.hljs-params {
  color: #bbbbbb
}
.hljs-punctuation,
.hljs-attr {
  color: #e6e6e6
}
.hljs-selector-tag,
.hljs-name,
.hljs-meta {
  color: #ff4b82
}
.hljs-operator,
.hljs-char.escape_ {
  color: #b084eb
}
.hljs-keyword,
.hljs-deletion {
  color: #ff75b5
}
.hljs-regexp,
.hljs-selector-pseudo,
.hljs-selector-attr,
.hljs-variable.language_ {
  color: #ff9ac1
}
.hljs-subst,
.hljs-property,
.hljs-code,
.hljs-formula,
.hljs-section,
.hljs-title.function_ {
  color: #45a9f9
}
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition,
.hljs-selector-class,
.hljs-title.class_,
.hljs-title.class_.inherited__,
.hljs-meta .hljs-string {
  color: #19f9d8
}
.hljs-variable,
.hljs-template-variable,
.hljs-number,
.hljs-literal,
.hljs-type,
.hljs-link,
.hljs-built_in,
.hljs-title,
.hljs-selector-id,
.hljs-tag,
.hljs-doctag,
.hljs-attribute,
.hljs-template-tag,
.hljs-meta .hljs-keyword,
.hljs-punctuation {
  color: #ffb86c
}