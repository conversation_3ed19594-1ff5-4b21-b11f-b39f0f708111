pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Zenburn
  Author: elnawe
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme zenburn
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #383838  Default Background
base01  #404040  Lighter Background (Used for status bars, line number and folding marks)
base02  #606060  Selection Background
base03  #6f6f6f  Comments, Invisibles, Line Highlighting
base04  #808080  Dark Foreground (Used for status bars)
base05  #dcdccc  Default Foreground, Caret, Delimiters, Operators
base06  #c0c0c0  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #dca3a3  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #dfaf8f  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #e0cf9f  Classes, Markup Bold, Search Text Background
base0B  #5f7f5f  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #93e0e3  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #7cb8bb  Functions, Methods, Attribute IDs, Headings
base0E  #dc8cc3  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #000000  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #dcdccc;
  background: #383838
}
.hljs::selection,
.hljs ::selection {
  background-color: #606060;
  color: #dcdccc
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6f6f6f -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6f6f6f
}
/* base04 - #808080 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #808080
}
/* base05 - #dcdccc -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #dcdccc
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #dca3a3
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #dfaf8f
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #e0cf9f
}
.hljs-strong {
  font-weight: bold;
  color: #e0cf9f
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #5f7f5f
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #93e0e3
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #7cb8bb
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #dc8cc3
}
.hljs-emphasis {
  color: #dc8cc3;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #000000
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}