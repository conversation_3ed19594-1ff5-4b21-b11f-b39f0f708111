pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Ashes
  Author: <PERSON><PERSON> (https://github.com/janniks)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme ashes
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1C2023  Default Background
base01  #393F45  Lighter Background (Used for status bars, line number and folding marks)
base02  #565E65  Selection Background
base03  #747C84  Comments, Invisibles, Line Highlighting
base04  #ADB3BA  Dark Foreground (Used for status bars)
base05  #C7CCD1  Default Foreground, Caret, Delimiters, Operators
base06  #DFE2E5  Light Foreground (Not often used)
base07  #F3F4F5  Light Background (Not often used)
base08  #C7AE95  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #C7C795  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #AEC795  Classes, Markup Bold, Search Text Background
base0B  #95C7AE  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #95AEC7  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #AE95C7  Functions, Methods, Attribute IDs, Headings
base0E  #C795AE  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #C79595  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #C7CCD1;
  background: #1C2023
}
.hljs::selection,
.hljs ::selection {
  background-color: #565E65;
  color: #C7CCD1
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #747C84 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #747C84
}
/* base04 - #ADB3BA -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #ADB3BA
}
/* base05 - #C7CCD1 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #C7CCD1
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #C7AE95
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #C7C795
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #AEC795
}
.hljs-strong {
  font-weight: bold;
  color: #AEC795
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #95C7AE
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #95AEC7
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #AE95C7
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #C795AE
}
.hljs-emphasis {
  color: #C795AE;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #C79595
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}