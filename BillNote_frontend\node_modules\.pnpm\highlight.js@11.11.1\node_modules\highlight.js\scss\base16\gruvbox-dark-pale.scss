pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Gruvbox dark, pale
  Author: <PERSON><PERSON><PERSON> (<EMAIL>), morhetz (https://github.com/morhetz/gruvbox)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme gruvbox-dark-pale
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #262626  Default Background
base01  #3a3a3a  Lighter Background (Used for status bars, line number and folding marks)
base02  #4e4e4e  Selection Background
base03  #8a8a8a  Comments, Invisibles, Line Highlighting
base04  #949494  Dark Foreground (Used for status bars)
base05  #dab997  Default Foreground, Caret, Delimiters, Operators
base06  #d5c4a1  Light Foreground (Not often used)
base07  #ebdbb2  Light Background (Not often used)
base08  #d75f5f  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff8700  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffaf00  Classes, Markup Bold, Search Text Background
base0B  #afaf00  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #85ad85  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #83adad  Functions, Methods, Attribute IDs, Headings
base0E  #d485ad  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #d65d0e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #dab997;
  background: #262626
}
.hljs::selection,
.hljs ::selection {
  background-color: #4e4e4e;
  color: #dab997
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #8a8a8a -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #8a8a8a
}
/* base04 - #949494 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #949494
}
/* base05 - #dab997 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #dab997
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d75f5f
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff8700
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffaf00
}
.hljs-strong {
  font-weight: bold;
  color: #ffaf00
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #afaf00
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #85ad85
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #83adad
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #d485ad
}
.hljs-emphasis {
  color: #d485ad;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #d65d0e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}