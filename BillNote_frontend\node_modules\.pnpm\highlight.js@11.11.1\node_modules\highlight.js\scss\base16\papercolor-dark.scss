pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: PaperColor Dark
  Author: <PERSON> (http://github.com/j<PERSON><PERSON><PERSON>) based on PaperColor Theme (https://github.com/NLKNguyen/papercolor-theme)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme papercolor-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1c1c1c  Default Background
base01  #af005f  Lighter Background (Used for status bars, line number and folding marks)
base02  #5faf00  Selection Background
base03  #d7af5f  Comments, Invisibles, Line Highlighting
base04  #5fafd7  Dark Foreground (Used for status bars)
base05  #808080  Default Foreground, Caret, Delimiters, Operators
base06  #d7875f  Light Foreground (Not often used)
base07  #d0d0d0  Light Background (Not often used)
base08  #585858  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #5faf5f  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #afd700  Classes, Markup Bold, Search Text Background
base0B  #af87d7  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #ffaf00  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #ff5faf  Functions, Methods, Attribute IDs, Headings
base0E  #00afaf  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #5f8787  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #808080;
  background: #1c1c1c
}
.hljs::selection,
.hljs ::selection {
  background-color: #5faf00;
  color: #808080
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #d7af5f -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #d7af5f
}
/* base04 - #5fafd7 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #5fafd7
}
/* base05 - #808080 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #808080
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #585858
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #5faf5f
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #afd700
}
.hljs-strong {
  font-weight: bold;
  color: #afd700
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #af87d7
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #ffaf00
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #ff5faf
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #00afaf
}
.hljs-emphasis {
  color: #00afaf;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #5f8787
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}