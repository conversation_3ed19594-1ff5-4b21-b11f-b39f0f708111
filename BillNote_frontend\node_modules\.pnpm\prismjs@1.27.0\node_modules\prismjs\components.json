{"core": {"meta": {"path": "components/prism-core.js", "option": "mandatory"}, "core": "Core"}, "themes": {"meta": {"path": "themes/{id}.css", "link": "index.html?theme={id}", "exclusive": true}, "prism": {"title": "<PERSON><PERSON><PERSON>", "option": "default"}, "prism-dark": "Dark", "prism-funky": "Funky", "prism-okaidia": {"title": "Okaidia", "owner": "ocodia"}, "prism-twilight": {"title": "Twilight", "owner": "<PERSON><PERSON><PERSON>"}, "prism-coy": {"title": "Coy", "owner": "t<PERSON>or"}, "prism-solarizedlight": {"title": "Solarized Light", "owner": "hectormatos2011 "}, "prism-tomorrow": {"title": "Tomorrow Night", "owner": "<PERSON><PERSON>"}}, "languages": {"meta": {"path": "components/prism-{id}", "noCSS": true, "examplesPath": "examples/prism-{id}", "addCheckAll": true}, "markup": {"title": "<PERSON><PERSON>", "alias": ["html", "xml", "svg", "mathml", "ssml", "atom", "rss"], "aliasTitles": {"html": "HTML", "xml": "XML", "svg": "SVG", "mathml": "MathML", "ssml": "SSML", "atom": "Atom", "rss": "RSS"}, "option": "default"}, "css": {"title": "CSS", "option": "default", "modify": "markup"}, "clike": {"title": "C-like", "option": "default"}, "javascript": {"title": "JavaScript", "require": "clike", "modify": "markup", "optional": "regex", "alias": "js", "option": "default"}, "abap": {"title": "ABAP", "owner": "<PERSON><PERSON><PERSON>"}, "abnf": {"title": "ABNF", "owner": "RunDevelopment"}, "actionscript": {"title": "ActionScript", "require": "javascript", "modify": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "ada": {"title": "Ada", "owner": "Lucretia"}, "agda": {"title": "Agda", "owner": "xy-ren"}, "al": {"title": "AL", "owner": "RunDevelopment"}, "antlr4": {"title": "ANTLR4", "alias": "g4", "owner": "RunDevelopment"}, "apacheconf": {"title": "Apache Configuration", "owner": "GuiTeK"}, "apex": {"title": "Apex", "require": ["clike", "sql"], "owner": "RunDevelopment"}, "apl": {"title": "APL", "owner": "ngn"}, "applescript": {"title": "AppleScript", "owner": "<PERSON><PERSON><PERSON>"}, "aql": {"title": "AQL", "owner": "RunDevelopment"}, "arduino": {"title": "<PERSON><PERSON><PERSON><PERSON>", "require": "cpp", "alias": "ino", "owner": "dkern"}, "arff": {"title": "ARFF", "owner": "<PERSON><PERSON><PERSON>"}, "asciidoc": {"alias": "adoc", "title": "AsciiDoc", "owner": "<PERSON><PERSON><PERSON>"}, "aspnet": {"title": "ASP.NET (C#)", "require": ["markup", "csharp"], "owner": "nauzilus"}, "asm6502": {"title": "6502 Assembly", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "asmatmel": {"title": "Atmel AVR Assembly", "owner": "cerkit"}, "autohotkey": {"title": "AutoHotkey", "owner": "aviaryan"}, "autoit": {"title": "AutoIt", "owner": "<PERSON><PERSON><PERSON>"}, "avisynth": {"title": "AviSynth", "alias": "avs", "owner": "Zinfidel"}, "avro-idl": {"title": "Avro IDL", "alias": "avdl", "owner": "RunDevelopment"}, "bash": {"title": "<PERSON><PERSON>", "alias": "shell", "aliasTitles": {"shell": "Shell"}, "owner": "zeitgeist87"}, "basic": {"title": "BASIC", "owner": "<PERSON><PERSON><PERSON>"}, "batch": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "bbcode": {"title": "BBcode", "alias": "shortcode", "aliasTitles": {"shortcode": "Shortcode"}, "owner": "RunDevelopment"}, "bicep": {"title": "Bicep", "owner": "johnny<PERSON><PERSON><PERSON>"}, "birb": {"title": "<PERSON><PERSON><PERSON>", "require": "clike", "owner": "Calamity210"}, "bison": {"title": "<PERSON>ison", "require": "c", "owner": "<PERSON><PERSON><PERSON>"}, "bnf": {"title": "BNF", "alias": "rbnf", "aliasTitles": {"rbnf": "RBNF"}, "owner": "RunDevelopment"}, "brainfuck": {"title": "Brainfuck", "owner": "<PERSON><PERSON><PERSON>"}, "brightscript": {"title": "BrightScript", "owner": "RunDevelopment"}, "bro": {"title": "<PERSON><PERSON>", "owner": "wayward710"}, "bsl": {"title": "BSL (1C:Enterprise)", "alias": "oscript", "aliasTitles": {"oscript": "OneScript"}, "owner": "Diversus23"}, "c": {"title": "C", "require": "clike", "owner": "zeitgeist87"}, "csharp": {"title": "C#", "require": "clike", "alias": ["cs", "dotnet"], "owner": "m<PERSON><PERSON><PERSON>"}, "cpp": {"title": "C++", "require": "c", "owner": "zeitgeist87"}, "cfscript": {"title": "CFScript", "require": "clike", "alias": "cfc", "owner": "mjclemente"}, "chaiscript": {"title": "ChaiScript", "require": ["clike", "cpp"], "owner": "RunDevelopment"}, "cil": {"title": "CIL", "owner": "sbrl"}, "clojure": {"title": "Clojure", "owner": "troglotit"}, "cmake": {"title": "CMake", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cobol": {"title": "COBOL", "owner": "RunDevelopment"}, "coffeescript": {"title": "CoffeeScript", "require": "javascript", "alias": "coffee", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "concurnas": {"title": "<PERSON><PERSON><PERSON><PERSON>", "alias": "conc", "owner": "jas<PERSON><PERSON><PERSON>"}, "csp": {"title": "Content-Security-Policy", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "coq": {"title": "Coq", "owner": "RunDevelopment"}, "crystal": {"title": "Crystal", "require": "ruby", "owner": "MakeNowJust"}, "css-extras": {"title": "CSS Extras", "require": "css", "modify": "css", "owner": "milesj"}, "csv": {"title": "CSV", "owner": "RunDevelopment"}, "cypher": {"title": "<PERSON><PERSON>", "owner": "RunDevelopment"}, "d": {"title": "D", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "dart": {"title": "Dart", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "dataweave": {"title": "DataWeave", "owner": "<PERSON><PERSON><PERSON>"}, "dax": {"title": "DAX", "owner": "peterbud"}, "dhall": {"title": "Dhall", "owner": "RunDevelopment"}, "diff": {"title": "Diff", "owner": "uranusjr"}, "django": {"title": "Django/Jinja2", "require": "markup-templating", "alias": "jinja2", "owner": "romanvm"}, "dns-zone-file": {"title": "DNS zone file", "owner": "RunDevelopment", "alias": "dns-zone"}, "docker": {"title": "<PERSON>er", "alias": "dockerfile", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "dot": {"title": "DOT (Graphviz)", "alias": "gv", "optional": "markup", "owner": "RunDevelopment"}, "ebnf": {"title": "EBNF", "owner": "RunDevelopment"}, "editorconfig": {"title": "EditorConfig", "owner": "osipxd"}, "eiffel": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "ejs": {"title": "EJS", "require": ["javascript", "markup-templating"], "owner": "RunDevelopment", "alias": "eta", "aliasTitles": {"eta": "Eta"}}, "elixir": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "elm": {"title": "Elm", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "etlua": {"title": "Embedded Lua templating", "require": ["lua", "markup-templating"], "owner": "RunDevelopment"}, "erb": {"title": "ERB", "require": ["ruby", "markup-templating"], "owner": "<PERSON><PERSON><PERSON>"}, "erlang": {"title": "Erl<PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "excel-formula": {"title": "Excel Formula", "alias": ["xlsx", "xls"], "owner": "RunDevelopment"}, "fsharp": {"title": "F#", "require": "clike", "owner": "simonreynolds7"}, "factor": {"title": "Factor", "owner": "catb0t"}, "false": {"title": "False", "owner": "edukisto"}, "firestore-security-rules": {"title": "Firestore security rules", "require": "clike", "owner": "RunDevelopment"}, "flow": {"title": "Flow", "require": "javascript", "owner": "<PERSON><PERSON><PERSON>"}, "fortran": {"title": "Fortran", "owner": "<PERSON><PERSON><PERSON>"}, "ftl": {"title": "FreeMarker Template Language", "require": "markup-templating", "owner": "RunDevelopment"}, "gml": {"title": "GameMaker Language", "alias": "gamemakerlanguage", "require": "clike", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "gap": {"title": "GAP (CAS)", "owner": "RunDevelopment"}, "gcode": {"title": "G-code", "owner": "RunDevelopment"}, "gdscript": {"title": "GDScript", "owner": "RunDevelopment"}, "gedcom": {"title": "GEDCOM", "owner": "<PERSON><PERSON><PERSON>"}, "gherkin": {"title": "<PERSON><PERSON><PERSON>", "owner": "hason"}, "git": {"title": "Git", "owner": "lgiraudel"}, "glsl": {"title": "GLSL", "require": "c", "owner": "<PERSON><PERSON><PERSON>"}, "gn": {"title": "GN", "alias": "gni", "owner": "RunDevelopment"}, "go": {"title": "Go", "require": "clike", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "go-module": {"title": "Go module", "alias": "go-mod", "owner": "RunDevelopment"}, "graphql": {"title": "GraphQL", "optional": "markdown", "owner": "<PERSON><PERSON><PERSON>"}, "groovy": {"title": "Groovy", "require": "clike", "owner": "robfletcher"}, "haml": {"title": "<PERSON><PERSON>", "require": "ruby", "optional": ["css", "css-extras", "coffeescript", "erb", "javascript", "less", "markdown", "scss", "textile"], "owner": "<PERSON><PERSON><PERSON>"}, "handlebars": {"title": "Handlebars", "require": "markup-templating", "alias": "hbs", "owner": "<PERSON><PERSON><PERSON>"}, "haskell": {"title": "<PERSON><PERSON>", "alias": "hs", "owner": "bholst"}, "haxe": {"title": "Haxe", "require": "clike", "optional": "regex", "owner": "<PERSON><PERSON><PERSON>"}, "hcl": {"title": "HCL", "owner": "<PERSON>is"}, "hlsl": {"title": "HLSL", "require": "c", "owner": "RunDevelopment"}, "hoon": {"title": "Hoon", "owner": "matildepark"}, "http": {"title": "HTTP", "optional": ["csp", "css", "hpkp", "hsts", "javascript", "json", "markup", "uri"], "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hpkp": {"title": "HTTP Public-Key-Pins", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "hsts": {"title": "HTTP Strict-Transport-Security", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "ichigojam": {"title": "IchigoJam", "owner": "BlueCocoa"}, "icon": {"title": "Icon", "owner": "<PERSON><PERSON><PERSON>"}, "icu-message-format": {"title": "ICU Message Format", "owner": "RunDevelopment"}, "idris": {"title": "<PERSON><PERSON><PERSON>", "alias": "idr", "owner": "KeenS", "require": "haskell"}, "ignore": {"title": ".ignore", "owner": "osipxd", "alias": ["gitignore", "hgignore", "npmignore"], "aliasTitles": {"gitignore": ".giti<PERSON>re", "hgignore": ".hgi<PERSON>re", "npmignore": ".n<PERSON><PERSON><PERSON>"}}, "inform7": {"title": "Inform 7", "owner": "<PERSON><PERSON><PERSON>"}, "ini": {"title": "Ini", "owner": "aviaryan"}, "io": {"title": "Io", "owner": "AlesTsurko"}, "j": {"title": "J", "owner": "<PERSON><PERSON><PERSON>"}, "java": {"title": "Java", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "javadoc": {"title": "JavaDoc", "require": ["markup", "java", "javadoclike"], "modify": "java", "optional": "scala", "owner": "RunDevelopment"}, "javadoclike": {"title": "JavaDoc-like", "modify": ["java", "javascript", "php"], "owner": "RunDevelopment"}, "javastacktrace": {"title": "Java stack trace", "owner": "RunDevelopment"}, "jexl": {"title": "<PERSON><PERSON><PERSON>", "owner": "czo<PERSON>"}, "jolie": {"title": "<PERSON><PERSON>", "require": "clike", "owner": "thesave"}, "jq": {"title": "JQ", "owner": "RunDevelopment"}, "jsdoc": {"title": "JSDoc", "require": ["javascript", "javadoclike", "typescript"], "modify": "javascript", "optional": ["actionscript", "coffeescript"], "owner": "RunDevelopment"}, "js-extras": {"title": "JS Extras", "require": "javascript", "modify": "javascript", "optional": ["actionscript", "coffeescript", "flow", "n4js", "typescript"], "owner": "RunDevelopment"}, "json": {"title": "JSON", "alias": "webmanifest", "aliasTitles": {"webmanifest": "Web App Manifest"}, "owner": "CupOfTea696"}, "json5": {"title": "JSON5", "require": "json", "owner": "RunDevelopment"}, "jsonp": {"title": "JSONP", "require": "json", "owner": "RunDevelopment"}, "jsstacktrace": {"title": "JS stack trace", "owner": "sbrl"}, "js-templates": {"title": "JS Templates", "require": "javascript", "modify": "javascript", "optional": ["css", "css-extras", "graphql", "markdown", "markup", "sql"], "owner": "RunDevelopment"}, "julia": {"title": "<PERSON>", "owner": "cdagnino"}, "keepalived": {"title": "Keepalived Configure", "owner": "dev-itsheng"}, "keyman": {"title": "Keyman", "owner": "mc<PERSON><PERSON><PERSON>"}, "kotlin": {"title": "<PERSON><PERSON><PERSON>", "alias": ["kt", "kts"], "aliasTitles": {"kts": "<PERSON><PERSON><PERSON>"}, "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "kumir": {"title": "<PERSON><PERSON><PERSON> (КуМир)", "alias": "kum", "owner": "edukisto"}, "kusto": {"title": "<PERSON><PERSON>", "owner": "RunDevelopment"}, "latex": {"title": "LaTeX", "alias": ["tex", "context"], "aliasTitles": {"tex": "TeX", "context": "ConTeXt"}, "owner": "j<PERSON><PERSON><PERSON>"}, "latte": {"title": "Latte", "require": ["clike", "markup-templating", "php"], "owner": "nette"}, "less": {"title": "Less", "require": "css", "optional": "css-extras", "owner": "<PERSON><PERSON><PERSON>"}, "lilypond": {"title": "Lily<PERSON>ond", "require": "scheme", "alias": "ly", "owner": "RunDevelopment"}, "liquid": {"title": "Liquid", "require": "markup-templating", "owner": "cinhtau"}, "lisp": {"title": "Lisp", "alias": ["emacs", "elisp", "emacs-lisp"], "owner": "JuanCaicedo"}, "livescript": {"title": "LiveScript", "owner": "<PERSON><PERSON><PERSON>"}, "llvm": {"title": "LLVM IR", "owner": "porglezomp"}, "log": {"title": "Log file", "optional": "javastacktrace", "owner": "RunDevelopment"}, "lolcode": {"title": "LOLCODE", "owner": "<PERSON><PERSON><PERSON>"}, "lua": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "magma": {"title": "Magma (CAS)", "owner": "RunDevelopment"}, "makefile": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "markdown": {"title": "<PERSON><PERSON>", "require": "markup", "optional": "yaml", "alias": "md", "owner": "<PERSON><PERSON><PERSON>"}, "markup-templating": {"title": "Markup templating", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "matlab": {"title": "MATLAB", "owner": "<PERSON><PERSON><PERSON>"}, "maxscript": {"title": "MAXScript", "owner": "RunDevelopment"}, "mel": {"title": "MEL", "owner": "<PERSON><PERSON><PERSON>"}, "mermaid": {"title": "Mermaid", "owner": "RunDevelopment"}, "mizar": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "mongodb": {"title": "MongoDB", "owner": "airs0urce", "require": "javascript"}, "monkey": {"title": "Monkey", "owner": "<PERSON><PERSON><PERSON>"}, "moonscript": {"title": "MoonScript", "alias": "moon", "owner": "RunDevelopment"}, "n1ql": {"title": "N1QL", "owner": "TMWilds"}, "n4js": {"title": "N4JS", "require": "javascript", "optional": "jsdoc", "alias": "n4jsd", "owner": "bsmith-n4"}, "nand2tetris-hdl": {"title": "Nand To Tetris HDL", "owner": "<PERSON><PERSON><PERSON>"}, "naniscript": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>", "alias": "nani"}, "nasm": {"title": "NASM", "owner": "rbmj"}, "neon": {"title": "NEON", "owner": "nette"}, "nevod": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nginx": {"title": "nginx", "owner": "volado"}, "nim": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "nix": {"title": "<PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "nsis": {"title": "NSIS", "owner": "<PERSON>berg"}, "objectivec": {"title": "Objective-C", "require": "c", "alias": "objc", "owner": "uranusjr"}, "ocaml": {"title": "OCaml", "owner": "<PERSON><PERSON><PERSON>"}, "opencl": {"title": "OpenCL", "require": "c", "modify": ["c", "cpp"], "owner": "Milania1"}, "openqasm": {"title": "OpenQasm", "alias": "qasm", "owner": "RunDevelopment"}, "oz": {"title": "Oz", "owner": "<PERSON><PERSON><PERSON>"}, "parigp": {"title": "PARI/GP", "owner": "<PERSON><PERSON><PERSON>"}, "parser": {"title": "<PERSON><PERSON><PERSON>", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "pascal": {"title": "<PERSON>", "alias": "objectpascal", "aliasTitles": {"objectpascal": "Object Pascal"}, "owner": "<PERSON><PERSON><PERSON>"}, "pascaligo": {"title": "Pascal<PERSON>", "owner": "DefinitelyNotAGoat"}, "psl": {"title": "PATROL Scripting Language", "owner": "be<PERSON><PERSON><PERSON>"}, "pcaxis": {"title": "PC-Axis", "alias": "px", "owner": "RunDevelopment"}, "peoplecode": {"title": "PeopleCode", "alias": "pcode", "owner": "RunDevelopment"}, "perl": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "php": {"title": "PHP", "require": "markup-templating", "owner": "milesj"}, "phpdoc": {"title": "PHPDoc", "require": ["php", "javadoclike"], "modify": "php", "owner": "RunDevelopment"}, "php-extras": {"title": "PHP Extras", "require": "php", "modify": "php", "owner": "milesj"}, "plsql": {"title": "PL/SQL", "require": "sql", "owner": "<PERSON><PERSON><PERSON>"}, "powerquery": {"title": "PowerQuery", "alias": ["pq", "mscript"], "owner": "peterbud"}, "powershell": {"title": "PowerShell", "owner": "nauzilus"}, "processing": {"title": "Processing", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "prolog": {"title": "Prolog", "owner": "<PERSON><PERSON><PERSON>"}, "promql": {"title": "PromQL", "owner": "aren<PERSON><PERSON><PERSON>"}, "properties": {"title": ".properties", "owner": "<PERSON><PERSON><PERSON>"}, "protobuf": {"title": "Protocol Buffers", "require": "clike", "owner": "just-boris"}, "pug": {"title": "<PERSON><PERSON>", "require": ["markup", "javascript"], "optional": ["coffeescript", "ejs", "handlebars", "less", "livescript", "markdown", "scss", "stylus", "twig"], "owner": "<PERSON><PERSON><PERSON>"}, "puppet": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "pure": {"title": "Pure", "optional": ["c", "cpp", "fortran"], "owner": "<PERSON><PERSON><PERSON>"}, "purebasic": {"title": "PureBasic", "require": "clike", "alias": "pbfasm", "owner": "HeX0R101"}, "purescript": {"title": "PureScript", "require": "haskell", "alias": "purs", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "python": {"title": "Python", "alias": "py", "owner": "multipetros"}, "qsharp": {"title": "Q#", "require": "clike", "alias": "qs", "owner": "<PERSON><PERSON><PERSON>"}, "q": {"title": "Q (kdb+ database)", "owner": "<PERSON><PERSON><PERSON>"}, "qml": {"title": "QML", "require": "javascript", "owner": "RunDevelopment"}, "qore": {"title": "<PERSON><PERSON>", "require": "clike", "owner": "temnroegg"}, "r": {"title": "R", "owner": "<PERSON><PERSON><PERSON>"}, "racket": {"title": "Racket", "require": "scheme", "alias": "rkt", "owner": "RunDevelopment"}, "cshtml": {"title": "Razor C#", "alias": "razor", "require": ["markup", "csharp"], "optional": ["css", "css-extras", "javascript", "js-extras"], "owner": "RunDevelopment"}, "jsx": {"title": "React JSX", "require": ["markup", "javascript"], "optional": ["jsdoc", "js-extras", "js-templates"], "owner": "vkbansal"}, "tsx": {"title": "React TSX", "require": ["jsx", "typescript"]}, "reason": {"title": "Reason", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "regex": {"title": "Regex", "owner": "RunDevelopment"}, "rego": {"title": "Rego", "owner": "JordanSh"}, "renpy": {"title": "Ren'py", "alias": "rpy", "owner": "HyuchiaDiego"}, "rest": {"title": "reST (reStructuredText)", "owner": "<PERSON><PERSON><PERSON>"}, "rip": {"title": "<PERSON><PERSON>", "owner": "r<PERSON><PERSON><PERSON>"}, "roboconf": {"title": "Roboconf", "owner": "<PERSON><PERSON><PERSON>"}, "robotframework": {"title": "Robot Framework", "alias": "robot", "owner": "RunDevelopment"}, "ruby": {"title": "<PERSON>", "require": "clike", "alias": "rb", "owner": "samflores"}, "rust": {"title": "Rust", "owner": "<PERSON><PERSON><PERSON>"}, "sas": {"title": "SAS", "optional": ["groovy", "lua", "sql"], "owner": "<PERSON><PERSON><PERSON>"}, "sass": {"title": "Sass (Sass)", "require": "css", "optional": "css-extras", "owner": "<PERSON><PERSON><PERSON>"}, "scss": {"title": "Sass (Scss)", "require": "css", "optional": "css-extras", "owner": "MoOx"}, "scala": {"title": "Scala", "require": "java", "owner": "jozic"}, "scheme": {"title": "Scheme", "owner": "bacchus123"}, "shell-session": {"title": "Shell session", "require": "bash", "alias": ["sh-session", "shellsession"], "owner": "RunDevelopment"}, "smali": {"title": "Smali", "owner": "RunDevelopment"}, "smalltalk": {"title": "Smalltalk", "owner": "<PERSON><PERSON><PERSON>"}, "smarty": {"title": "Smarty", "require": "markup-templating", "optional": "php", "owner": "<PERSON><PERSON><PERSON>"}, "sml": {"title": "SML", "alias": "smlnj", "aliasTitles": {"smlnj": "SML/NJ"}, "owner": "RunDevelopment"}, "solidity": {"title": "Solidity (Ethereum)", "alias": "sol", "require": "clike", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "solution-file": {"title": "Solution file", "alias": "sln", "owner": "RunDevelopment"}, "soy": {"title": "Soy (Closure Template)", "require": "markup-templating", "owner": "<PERSON><PERSON><PERSON>"}, "sparql": {"title": "SPARQL", "require": "turtle", "owner": "<PERSON><PERSON>-<PERSON>", "alias": "rq"}, "splunk-spl": {"title": "Splunk SPL", "owner": "RunDevelopment"}, "sqf": {"title": "SQF: Status Quo Function (Arma 3)", "require": "clike", "owner": "RunDevelopment"}, "sql": {"title": "SQL", "owner": "multipetros"}, "squirrel": {"title": "Squirrel", "require": "clike", "owner": "RunDevelopment"}, "stan": {"title": "<PERSON>", "owner": "RunDevelopment"}, "iecst": {"title": "Structured Text (IEC 61131-3)", "owner": "ser<PERSON><PERSON><PERSON>o"}, "stylus": {"title": "<PERSON><PERSON><PERSON>", "owner": "vkbansal"}, "swift": {"title": "Swift", "owner": "chris<PERSON><PERSON>"}, "systemd": {"title": "Systemd configuration file", "owner": "RunDevelopment"}, "t4-templating": {"title": "T4 templating", "owner": "RunDevelopment"}, "t4-cs": {"title": "T4 Text Templates (C#)", "require": ["t4-templating", "csharp"], "alias": "t4", "owner": "RunDevelopment"}, "t4-vb": {"title": "T4 Text Templates (VB)", "require": ["t4-templating", "vbnet"], "owner": "RunDevelopment"}, "tap": {"title": "TAP", "owner": "isaacs", "require": "yaml"}, "tcl": {"title": "Tcl", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tt2": {"title": "Template Toolkit 2", "require": ["clike", "markup-templating"], "owner": "gflohr"}, "textile": {"title": "Textile", "require": "markup", "optional": "css", "owner": "<PERSON><PERSON><PERSON>"}, "toml": {"title": "TOML", "owner": "RunDevelopment"}, "tremor": {"title": "Tremor", "alias": ["trickle", "troy"], "owner": "da<PERSON>h", "aliasTitles": {"trickle": "trickle", "troy": "troy"}}, "turtle": {"title": "Turtle", "alias": "trig", "aliasTitles": {"trig": "TriG"}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "twig": {"title": "Twig", "require": "markup-templating", "owner": "brandonkelly"}, "typescript": {"title": "TypeScript", "require": "javascript", "optional": "js-templates", "alias": "ts", "owner": "vkbansal"}, "typoscript": {"title": "TypoScript", "alias": "tsconfig", "aliasTitles": {"tsconfig": "TSConfig"}, "owner": "dkern"}, "unrealscript": {"title": "UnrealScript", "alias": ["uscript", "uc"], "owner": "RunDevelopment"}, "uorazor": {"title": "UO Razor <PERSON>", "owner": "jaseowns"}, "uri": {"title": "URI", "alias": "url", "aliasTitles": {"url": "URL"}, "owner": "RunDevelopment"}, "v": {"title": "V", "require": "clike", "owner": "taggon"}, "vala": {"title": "Vala", "require": "clike", "optional": "regex", "owner": "TemplarVolk"}, "vbnet": {"title": "VB.Net", "require": "basic", "owner": "Bigsby"}, "velocity": {"title": "Velocity", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "verilog": {"title": "Verilog", "owner": "a-rey"}, "vhdl": {"title": "VHDL", "owner": "a-rey"}, "vim": {"title": "vim", "owner": "westonganger"}, "visual-basic": {"title": "Visual Basic", "alias": ["vb", "vba"], "aliasTitles": {"vba": "VBA"}, "owner": "<PERSON><PERSON><PERSON>"}, "warpscript": {"title": "WarpScript", "owner": "RunDevelopment"}, "wasm": {"title": "WebAssembly", "owner": "<PERSON><PERSON><PERSON>"}, "web-idl": {"title": "Web IDL", "alias": "webidl", "owner": "RunDevelopment"}, "wiki": {"title": "Wiki markup", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "wolfram": {"title": "Wolfram language", "alias": ["mathematica", "nb", "wl"], "aliasTitles": {"mathematica": "Mathematica", "nb": "Mathematica Notebook"}, "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "wren": {"title": "Wren", "owner": "clsource"}, "xeora": {"title": "Xeora", "require": "markup", "alias": "xeoracube", "aliasTitles": {"xeoracube": "XeoraCube"}, "owner": "<PERSON><PERSON><PERSON>"}, "xml-doc": {"title": "XML doc (.net)", "require": "markup", "modify": ["csharp", "fsharp", "vbnet"], "owner": "RunDevelopment"}, "xojo": {"title": "<PERSON><PERSON><PERSON> (REALbasic)", "owner": "<PERSON><PERSON><PERSON>"}, "xquery": {"title": "<PERSON><PERSON><PERSON><PERSON>", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "yaml": {"title": "YAML", "alias": "yml", "owner": "hason"}, "yang": {"title": "YANG", "owner": "RunDevelopment"}, "zig": {"title": "Zig", "owner": "RunDevelopment"}}, "plugins": {"meta": {"path": "plugins/{id}/prism-{id}", "link": "plugins/{id}/"}, "line-highlight": {"title": "Line Highlight", "description": "Highlights specific lines and/or line ranges."}, "line-numbers": {"title": "Line Numbers", "description": "Line number at the beginning of code lines.", "owner": "kuba-kubula"}, "show-invisibles": {"title": "Show Invisibles", "description": "Show hidden characters such as tabs and line breaks.", "optional": ["autolinker", "data-uri-highlight"]}, "autolinker": {"title": "Autolinker", "description": "Converts URLs and emails in code to clickable links. Parses Markdown links in comments."}, "wpd": {"title": "WebPlatform Docs", "description": "Makes tokens link to <a href=\"https://webplatform.github.io/docs/\">WebPlatform.org documentation</a>. The links open in a new tab."}, "custom-class": {"title": "Custom Class", "description": "This plugin allows you to prefix Prism's default classes (<code>.comment</code> can become <code>.namespace--comment</code>) or replace them with your defined ones (like <code>.editor__comment</code>). You can even add new classes.", "owner": "dvkndn", "noCSS": true}, "file-highlight": {"title": "File Highlight", "description": "Fetch external files and highlight them with Prism. Used on the Prism website itself.", "noCSS": true}, "show-language": {"title": "Show Language", "description": "Display the highlighted language in code blocks (inline code does not show the label).", "owner": "nauzilus", "noCSS": true, "require": "toolbar"}, "jsonp-highlight": {"title": "JSONP Highlight", "description": "Fetch content with JSONP and highlight some interesting content (e.g. GitHub/Gists or Bitbucket API).", "noCSS": true, "owner": "nauzilus"}, "highlight-keywords": {"title": "Highlight Keywords", "description": "Adds special CSS classes for each keyword for fine-grained highlighting.", "owner": "vkbansal", "noCSS": true}, "remove-initial-line-feed": {"title": "Remove initial line feed", "description": "Removes the initial line feed in code blocks.", "owner": "<PERSON><PERSON><PERSON>", "noCSS": true}, "inline-color": {"title": "Inline color", "description": "Adds a small inline preview for colors in style sheets.", "require": "css-extras", "owner": "RunDevelopment"}, "previewers": {"title": "Previewers", "description": "Previewers for angles, colors, gradients, easing and time.", "require": "css-extras", "owner": "<PERSON><PERSON><PERSON>"}, "autoloader": {"title": "Autoloader", "description": "Automatically loads the needed languages to highlight the code blocks.", "owner": "<PERSON><PERSON><PERSON>", "noCSS": true}, "keep-markup": {"title": "Keep Markup", "description": "Prevents custom markup from being dropped out during highlighting.", "owner": "<PERSON><PERSON><PERSON>", "optional": "normalize-whitespace", "noCSS": true}, "command-line": {"title": "Command Line", "description": "Display a command line with a prompt and, optionally, the output/response from the commands.", "owner": "chriswells0"}, "unescaped-markup": {"title": "Unescaped Markup", "description": "Write markup without having to escape anything."}, "normalize-whitespace": {"title": "Normalize Whitespace", "description": "Supports multiple operations to normalize whitespace in code blocks.", "owner": "zeitgeist87", "optional": "unescaped-markup", "noCSS": true}, "data-uri-highlight": {"title": "Data-URI Highlight", "description": "Highlights data-URI contents.", "owner": "<PERSON><PERSON><PERSON>", "noCSS": true}, "toolbar": {"title": "<PERSON><PERSON><PERSON>", "description": "Attach a toolbar for plugins to easily register buttons on the top of a code block.", "owner": "mAAdha<PERSON>ah"}, "copy-to-clipboard": {"title": "<PERSON><PERSON> to Clipboard Button", "description": "Add a button that copies the code block to the clipboard when clicked.", "owner": "mAAdha<PERSON>ah", "require": "toolbar", "noCSS": true}, "download-button": {"title": "Download Button", "description": "A button in the toolbar of a code block adding a convenient way to download a code file.", "owner": "<PERSON><PERSON><PERSON>", "require": "toolbar", "noCSS": true}, "match-braces": {"title": "Match braces", "description": "Highlights matching braces.", "owner": "RunDevelopment"}, "diff-highlight": {"title": "<PERSON>ff Highlight", "description": "Highlights the code inside diff blocks.", "owner": "RunDevelopment", "require": "diff"}, "filter-highlight-all": {"title": "Filter <PERSON>ll", "description": "Filters the elements the <code>highlightAll</code> and <code>highlightAllUnder</code> methods actually highlight.", "owner": "RunDevelopment", "noCSS": true}, "treeview": {"title": "Treeview", "description": "A language with special styles to highlight file system tree structures.", "owner": "<PERSON><PERSON><PERSON>"}}}