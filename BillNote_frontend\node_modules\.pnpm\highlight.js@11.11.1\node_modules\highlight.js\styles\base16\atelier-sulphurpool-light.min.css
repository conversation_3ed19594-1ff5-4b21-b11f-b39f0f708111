/*!
  Theme: Atelier Sulphurpool Light
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#5e6687;background:#f5f7ff}.hljs ::selection,.hljs::selection{background-color:#979db4;color:#5e6687}.hljs-comment{color:#898ea4}.hljs-tag{color:#6b7394}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#5e6687}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#c94922}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#c76b29}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#c08b30}.hljs-strong{font-weight:700;color:#c08b30}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#ac9739}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#22a2c9}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#3d8fd1}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#6679cc}.hljs-emphasis{color:#6679cc;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#9c637a}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}