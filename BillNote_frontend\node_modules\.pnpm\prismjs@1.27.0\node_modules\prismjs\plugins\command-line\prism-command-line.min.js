!function(){if("undefined"!=typeof Prism&&"undefined"!=typeof document){var v=/(?:^|\s)command-line(?:\s|$)/,g="command-line-prompt",p="".startsWith?function(e,t){return e.startsWith(t)}:function(e,t){return 0===e.indexOf(t)},d="".endsWith?function(e,t){return e.endsWith(t)}:function(e,t){var n=e.length;return e.substring(n-t.length,n)===t};Prism.hooks.add("before-highlight",function(e){var t=N(e);if(!t.complete&&e.code){var n=e.element.parentElement;if(n&&/pre/i.test(n.nodeName)&&(v.test(n.className)||v.test(e.element.className))){var a=e.element.querySelector("."+g);a&&a.remove();var i=e.code.split("\n"),r=t.continuationLineIndicies=new Set,s=n.getAttribute("data-continuation-str");if(s&&1<i.length)for(var o=1;o<i.length;o++)i.hasOwnProperty(o-1)&&d(i[o-1],s)&&r.add(o);t.numberOfLines=i.length;var l=t.outputLines=[],m=n.getAttribute("data-output"),u=n.getAttribute("data-filter-output");if(null!==m)m.split(",").forEach(function(e){var t=e.split("-"),n=parseInt(t[0],10),a=2===t.length?parseInt(t[1],10):n;if(!isNaN(n)&&!isNaN(a)){n<1&&(n=1),a>i.length&&(a=i.length),a--;for(var r=--n;r<=a;r++)l[r]=i[r],i[r]=""}});else if(u)for(var c=0;c<i.length;c++)p(i[c],u)&&(l[c]=i[c].slice(u.length),i[c]="");e.code=i.join("\n")}else t.complete=!0}else t.complete=!0}),Prism.hooks.add("before-insert",function(e){var t=N(e);if(!t.complete){for(var n=e.highlightedCode.split("\n"),a=t.outputLines||[],r=0,i=n.length;r<i;r++)a.hasOwnProperty(r)?n[r]='<span class="token output">'+Prism.util.encode(a[r])+"</span>":n[r]='<span class="token command">'+n[r]+"</span>";e.highlightedCode=n.join("\n")}}),Prism.hooks.add("complete",function(e){if(function(e){return"command-line"in(e.vars=e.vars||{})}(e)){var t=N(e);if(!t.complete){var n=e.element.parentElement;v.test(e.element.className)&&(e.element.className=e.element.className.replace(v," ")),v.test(n.className)||(n.className+=" command-line");var a,r="",i=t.numberOfLines||0,s=h("data-prompt","");if(""!==s)a='<span data-prompt="'+s+'"></span>';else a='<span data-user="'+h("data-user","user")+'" data-host="'+h("data-host","localhost")+'"></span>';for(var o=t.continuationLineIndicies||new Set,l='<span data-continuation-prompt="'+h("data-continuation-prompt",">")+'"></span>',m=0;m<i;m++)o.has(m)?r+=l:r+=a;var u=document.createElement("span");u.className=g,u.innerHTML=r;for(var c=t.outputLines||[],p=0,d=c.length;p<d;p++)if(c.hasOwnProperty(p)){var f=u.children[p];f.removeAttribute("data-user"),f.removeAttribute("data-host"),f.removeAttribute("data-prompt")}e.element.insertBefore(u,e.element.firstChild),t.complete=!0}}function h(e,t){return(n.getAttribute(e)||t).replace(/"/g,"&quot")}})}function N(e){var t=e.vars=e.vars||{};return t["command-line"]=t["command-line"]||{}}}();