pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Snazzy
  Author: <PERSON><PERSON><PERSON> <PERSON> (https://github.com/chawyeh<PERSON>) based on Hyper Snazzy Theme (https://github.com/sindresorhus/hyper-snazzy)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme snazzy
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #282a36  Default Background
base01  #34353e  Lighter Background (Used for status bars, line number and folding marks)
base02  #43454f  Selection Background
base03  #78787e  Comments, Invisibles, Line Highlighting
base04  #a5a5a9  Dark Foreground (Used for status bars)
base05  #e2e4e5  Default Foreground, <PERSON>t, Delimiters, Operators
base06  #eff0eb  Light Foreground (Not often used)
base07  #f1f1f0  Light Background (Not often used)
base08  #ff5c57  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff9f43  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f3f99d  Classes, Markup Bold, Search Text Background
base0B  #5af78e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #9aedfe  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #57c7ff  Functions, Methods, Attribute IDs, Headings
base0E  #ff6ac1  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b2643c  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e2e4e5;
  background: #282a36
}
.hljs::selection,
.hljs ::selection {
  background-color: #43454f;
  color: #e2e4e5
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #78787e -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #78787e
}
/* base04 - #a5a5a9 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #a5a5a9
}
/* base05 - #e2e4e5 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e2e4e5
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ff5c57
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff9f43
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f3f99d
}
.hljs-strong {
  font-weight: bold;
  color: #f3f99d
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #5af78e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #9aedfe
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #57c7ff
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ff6ac1
}
.hljs-emphasis {
  color: #ff6ac1;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b2643c
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}