pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
 * Theme: FelipeC
 * Author: (c) 2021 <PERSON> <<EMAIL>>
 * Website: https://github.com/felipec/vim-felipec
 *
 * Autogenerated with vim-felipec's generator.
*/
.hljs {
  color: #dedde4;
  background-color: #1d1c21
}
.hljs::selection,
.hljs ::selection {
  color: #1d1c21;
  background-color: #ba9cef
}
.hljs-comment,
.hljs-code,
.hljs-quote {
  color: #9e9da4
}
.hljs-number,
.hljs-literal,
.hljs-deletion {
  color: #f09080
}
.hljs-punctuation,
.hljs-meta,
.hljs-operator,
.hljs-subst,
.hljs-doctag,
.hljs-template-variable,
.hljs-selector-attr {
  color: #ffbb7b
}
.hljs-type {
  color: #fddb7c
}
.hljs-tag,
.hljs-title,
.hljs-selector-class,
.hljs-selector-id {
  color: #c4da7d
}
.hljs-string,
.hljs-regexp,
.hljs-addition {
  color: #93e4a4
}
.hljs-class,
.hljs-property {
  color: #65e7d1
}
.hljs-name,
.hljs-selector-tag {
  color: #30c2d8
}
.hljs-keyword,
.hljs-built_in {
  color: #5fb8f2
}
.hljs-section,
.hljs-bullet {
  color: #90aafa
}
.hljs-selector-pseudo {
  color: #ba9cef
}
.hljs-variable,
.hljs-params,
.hljs-attr,
.hljs-attribute {
  color: #d991d2
}
.hljs-symbol,
.hljs-link {
  color: #ec8dab
}
.hljs-strong,
.hljs-literal,
.hljs-title {
  font-weight: bold
}
.hljs-emphasis {
  font-style: italic
}