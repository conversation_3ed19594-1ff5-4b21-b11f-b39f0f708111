pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: vulcan
  Author: <PERSON><PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme vulcan
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #041523  Default Background
base01  #122339  Lighter Background (Used for status bars, line number and folding marks)
base02  #003552  Selection Background
base03  #7a5759  Comments, Invisibles, Line Highlighting
base04  #6b6977  Dark Foreground (Used for status bars)
base05  #5b778c  Default Foreground, Caret, Delimiters, Operators
base06  #333238  Light Foreground (Not often used)
base07  #214d68  Light Background (Not often used)
base08  #818591  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #9198a3  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #adb4b9  Classes, Markup Bold, Search Text Background
base0B  #977d7c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #977d7c  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #977d7c  Functions, Methods, Attribute IDs, Headings
base0E  #9198a3  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #977d7c  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #5b778c;
  background: #041523
}
.hljs::selection,
.hljs ::selection {
  background-color: #003552;
  color: #5b778c
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #7a5759 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #7a5759
}
/* base04 - #6b6977 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #6b6977
}
/* base05 - #5b778c -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #5b778c
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #818591
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #9198a3
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #adb4b9
}
.hljs-strong {
  font-weight: bold;
  color: #adb4b9
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #977d7c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #977d7c
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #977d7c
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9198a3
}
.hljs-emphasis {
  color: #9198a3;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #977d7c
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}