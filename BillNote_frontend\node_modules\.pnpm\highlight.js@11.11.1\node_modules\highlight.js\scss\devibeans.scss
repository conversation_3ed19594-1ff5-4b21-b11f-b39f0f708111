pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
    Theme: devibeans (dark)
    Author: @terminaldweller
    Maintainer: @terminaldweller

    Inspired by vim's jellybeans theme (https://github.com/nanotech/jellybeans.vim)
*/
.hljs {
  background: #000000;
  color: #a39e9b
}
.hljs-attr,
.hljs-template-tag {
  color: #8787d7
}
.hljs-comment,
.hljs-doctag,
.hljs-quote {
  color: #339966
}
.hljs-params {
  color: #a39e9b
}
.hljs-regexp {
  color: #d700ff
}
.hljs-tag,
.hljs-selector-id,
.hljs-number,
.hljs-literal {
  color: #ef5350
}
.hljs-meta,
.hljs-meta .hljs-keyword {
  color: #0087ff
}
/* opt-out */
.hljs-operator,
.hljs-punctuation {
  
}
.hljs-selector-class,
.hljs-code,
.hljs-formula,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-link,
.hljs-keyword {
  color: #64b5f6
}
.hljs-built_in,
.hljs-title,
.hljs-deletion {
  color: #ff8700
}
.hljs-type,
.hljs-section,
.hljs-function,
.hljs-name,
.hljs-property,
.hljs-attribute {
  color: #ffd75f
}
.hljs-meta .hljs-string,
.hljs-string,
.hljs-subst,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
  color: #558b2f
}
.hljs-selector-tag {
  color: #9966ff
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}