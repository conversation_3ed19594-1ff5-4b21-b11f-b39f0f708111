## 更新日志

### 1.1.0

*2020-03-30*

#### BUG修复

- 重写内部 getIndex 方法
  - 修复某些情况下，匹配字符没有按顺序的问题[#7](https://github.com/xmflswood/pinyin-match/issues/7)

### 1.1.4

*2020-12-09*

#### 优化
- 移除多音字 大(tai)的读音
- 调整打包为rollup 支持es

### 1.2.0
*2021-02-18*

#### 拓展
- 新增繁体版本

### 1.2.3
*2023-02-12*

#### 优化
- 增加喆、钭
- 移除多音字 体(ben)的读音

### 1.2.4
*2023-02-25*

#### 优化
- 优化拼音中 ü 的 u v打法，详见 [issues/37](https://github.com/xmflswood/pinyin-match/issues/37)

### 1.2.5
*2023-11-25*

#### 优化
- 优化拼音中 ü 的 u v打法，详见 [输入时的 nv和lv识别失败问题](https://github.com/xmflswood/pinyin-match/pull/43)

### 1.2.6
*2024-08-29*

#### 优化
- 简体字典增加 “啰”

### 1.2.7
*2025-04-29*

#### 优化
- Unicode 标准化匹配问题 https://github.com/xmflswood/pinyin-match/issues/51

### 1.2.8
*2025-05-11*

#### 优化
- 简体字典胖增加'pang'读音
- 调整为MIT https://github.com/xmflswood/pinyin-match/issues/50