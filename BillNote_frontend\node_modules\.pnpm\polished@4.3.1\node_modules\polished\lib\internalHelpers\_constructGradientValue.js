"use strict";

exports.__esModule = true;
exports["default"] = void 0;
function constructGradientValue(literals) {
  var template = '';
  for (var _len = arguments.length, substitutions = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    substitutions[_key - 1] = arguments[_key];
  }
  for (var i = 0; i < literals.length; i += 1) {
    template += literals[i];
    if (i === substitutions.length - 1 && substitutions[i]) {
      var definedValues = substitutions.filter(function (substitute) {
        return !!substitute;
      });
      // Adds leading coma if properties preceed color-stops
      if (definedValues.length > 1) {
        template = template.slice(0, -1);
        template += ", " + substitutions[i];
        // No trailing space if color-stops is the only param provided
      } else if (definedValues.length === 1) {
        template += "" + substitutions[i];
      }
    } else if (substitutions[i]) {
      template += substitutions[i] + " ";
    }
  }
  return template.trim();
}
var _default = exports["default"] = constructGradientValue;
module.exports = exports.default;