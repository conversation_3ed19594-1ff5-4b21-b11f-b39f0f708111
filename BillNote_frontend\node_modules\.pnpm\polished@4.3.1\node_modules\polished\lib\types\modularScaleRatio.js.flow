// @flow

// Note: we define properties with JSdoc since documentation.js doesn't recognize
// exported types yet. See https://github.com/documentationjs/documentation/issues/680

/**
 * @property {number, 'minorSecond', 'majorSecond', 'minorThird', 'majorThird', 'perfectFourth', 'augFourth', 'perfectFifth', 'minorSixth', 'goldenSection', 'majorSixth', 'minorSeventh', 'majorSeventh', 'octave', 'majorTenth', 'majorEleventh', 'majorTwelfth', 'doubleOctave'} ModularScaleRatio
 */
export type ModularScaleRatio =
  | number
  | 'minorSecond'
  | 'majorSecond'
  | 'minorThird'
  | 'majorThird'
  | 'perfectFourth'
  | 'augFourth'
  | 'perfectFifth'
  | 'minorSixth'
  | 'goldenSection'
  | 'majorSixth'
  | 'minorSeventh'
  | 'majorSeventh'
  | 'octave'
  | 'majorTenth'
  | 'majorEleventh'
  | 'majorTwelfth'
  | 'doubleOctave'
