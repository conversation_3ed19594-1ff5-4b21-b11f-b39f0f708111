pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Windows 10
  Author: <PERSON> (https://github.com/C-Fergus)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme windows-10
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #0c0c0c  Default Background
base01  #2f2f2f  Lighter Background (Used for status bars, line number and folding marks)
base02  #535353  Selection Background
base03  #767676  Comments, Invisibles, Line Highlighting
base04  #b9b9b9  Dark Foreground (Used for status bars)
base05  #cccccc  Default Foreground, Caret, Delimiters, Operators
base06  #dfdfdf  Light Foreground (Not often used)
base07  #f2f2f2  Light Background (Not often used)
base08  #e74856  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #c19c00  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f9f1a5  Classes, Markup Bold, Search Text Background
base0B  #16c60c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #61d6d6  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3b78ff  Functions, Methods, Attribute IDs, Headings
base0E  #b4009e  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #13a10e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #cccccc;
  background: #0c0c0c
}
.hljs::selection,
.hljs ::selection {
  background-color: #535353;
  color: #cccccc
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #767676 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #767676
}
/* base04 - #b9b9b9 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b9b9b9
}
/* base05 - #cccccc -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #cccccc
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e74856
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #c19c00
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f9f1a5
}
.hljs-strong {
  font-weight: bold;
  color: #f9f1a5
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #16c60c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #61d6d6
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3b78ff
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #b4009e
}
.hljs-emphasis {
  color: #b4009e;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #13a10e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}