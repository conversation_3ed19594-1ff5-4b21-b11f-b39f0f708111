declare interface ContrastScores {
  AA: boolean;
  AALarge: boolean;
  AAA: boolean;
  AAALarge: boolean;
}
declare interface HslColor {
  hue: number;
  saturation: number;
  lightness: number;
}
declare interface HslaColor {
  hue: number;
  saturation: number;
  lightness: number;
  alpha: number;
}
declare interface RgbColor {
  red: number;
  green: number;
  blue: number;
}
declare interface RgbaColor {
  red: number;
  green: number;
  blue: number;
  alpha: number;
}

export { ContrastScores };
export { HslColor };
export { HslaColor };
export { RgbColor };
export { RgbaColor };
